<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间聚合逻辑验证测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .data-table {
            font-size: 0.9em;
        }
        .sql-preview {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .expected-result {
            background: #f0f8ff;
            border: 1px solid #b0d4f1;
            border-radius: 6px;
            padding: 15px;
        }
        .highlight-hour {
            background-color: #fff3cd !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">时间聚合逻辑验证测试</h1>
        
        <div class="alert alert-info">
            <h5>📋 测试目的</h5>
            <p>验证我们的SQL逻辑是否正确处理时间间隔内的数据聚合，确保能够获取每个时间段内的最大累计值。</p>
        </div>

        <!-- 示例数据 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h5>📊 示例原始数据 (data_history表)</h5>
                <table class="table table-striped table-sm data-table">
                    <thead>
                        <tr>
                            <th>device_name</th>
                            <th>timestamp</th>
                            <th>value (累计值)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="highlight-hour">
                            <td>设备A</td>
                            <td>2025-07-29 09:15:23</td>
                            <td>100</td>
                        </tr>
                        <tr class="highlight-hour">
                            <td>设备A</td>
                            <td>2025-07-29 09:32:45</td>
                            <td>150</td>
                        </tr>
                        <tr class="highlight-hour">
                            <td>设备A</td>
                            <td>2025-07-29 09:58:12</td>
                            <td>200</td>
                        </tr>
                        <tr>
                            <td>设备A</td>
                            <td>2025-07-29 10:05:30</td>
                            <td>220</td>
                        </tr>
                        <tr>
                            <td>设备A</td>
                            <td>2025-07-29 10:25:15</td>
                            <td>280</td>
                        </tr>
                        <tr>
                            <td>设备A</td>
                            <td>2025-07-29 10:55:40</td>
                            <td>350</td>
                        </tr>
                        <tr>
                            <td>设备B</td>
                            <td>2025-07-29 09:20:10</td>
                            <td>80</td>
                        </tr>
                        <tr>
                            <td>设备B</td>
                            <td>2025-07-29 09:45:30</td>
                            <td>120</td>
                        </tr>
                        <tr>
                            <td>设备B</td>
                            <td>2025-07-29 10:10:20</td>
                            <td>160</td>
                        </tr>
                    </tbody>
                </table>
                <small class="text-muted">黄色高亮：09:00-09:59时间段内的数据</small>
            </div>
            
            <div class="col-md-6">
                <h5>🎯 预期聚合结果 (interval_max CTE)</h5>
                <table class="table table-success table-sm data-table">
                    <thead>
                        <tr>
                            <th>device_name</th>
                            <th>time_interval</th>
                            <th>max_value</th>
                            <th>record_count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>设备A</td>
                            <td>2025-07-29 09:00:00</td>
                            <td>200</td>
                            <td>3</td>
                        </tr>
                        <tr>
                            <td>设备A</td>
                            <td>2025-07-29 10:00:00</td>
                            <td>350</td>
                            <td>3</td>
                        </tr>
                        <tr>
                            <td>设备B</td>
                            <td>2025-07-29 09:00:00</td>
                            <td>120</td>
                            <td>2</td>
                        </tr>
                        <tr>
                            <td>设备B</td>
                            <td>2025-07-29 10:00:00</td>
                            <td>160</td>
                            <td>1</td>
                        </tr>
                    </tbody>
                </table>
                
                <h5 class="mt-4">📈 最终差值计算结果</h5>
                <table class="table table-warning table-sm data-table">
                    <thead>
                        <tr>
                            <th>device_name</th>
                            <th>time_interval</th>
                            <th>interval_output</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>设备A</td>
                            <td>2025-07-29 10:00:00</td>
                            <td>150 (350-200)</td>
                        </tr>
                        <tr>
                            <td>设备B</td>
                            <td>2025-07-29 10:00:00</td>
                            <td>40 (160-120)</td>
                        </tr>
                    </tbody>
                </table>
                <small class="text-muted">注：第一个时间段没有前值，所以不显示</small>
            </div>
        </div>

        <!-- 生成的SQL -->
        <div class="mb-4">
            <h5>🔍 生成的SQL查询</h5>
            <div class="sql-preview" id="generatedSQL">
WITH interval_max AS (
    SELECT 
        device_name,
        DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00') as time_interval,
        MAX(value) as max_value,
        COUNT(*) as record_count  -- 显示该时间段内的记录数，便于调试
    FROM data_history
    GROUP BY device_name, DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00')
    ORDER BY device_name, time_interval
),
interval_diff AS (
    SELECT 
        device_name,
        time_interval,
        max_value,
        LAG(max_value, 1, 0) OVER (
            PARTITION BY device_name 
            ORDER BY time_interval
        ) as prev_value,
        max_value - LAG(max_value, 1, 0) OVER (
            PARTITION BY device_name 
            ORDER BY time_interval
        ) as interval_output
    FROM interval_max
)
SELECT 
    device_name,
    time_interval,
    CASE WHEN interval_output < 0 THEN 0 ELSE interval_output END as interval_output
FROM interval_diff
WHERE interval_output IS NOT NULL
ORDER BY device_name, time_interval DESC
            </div>
        </div>

        <!-- 逻辑验证 -->
        <div class="expected-result">
            <h5>✅ 逻辑验证结果</h5>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>第一步：时间标准化和分组</h6>
                    <ul>
                        <li><code>09:15:23</code> → <code>09:00:00</code></li>
                        <li><code>09:32:45</code> → <code>09:00:00</code></li>
                        <li><code>09:58:12</code> → <code>09:00:00</code></li>
                    </ul>
                    <p><strong>结果</strong>：所有09点的数据被分到同一组</p>
                </div>
                
                <div class="col-md-6">
                    <h6>第二步：组内聚合</h6>
                    <ul>
                        <li>设备A 09:00:00组：MAX(100, 150, 200) = <strong>200</strong></li>
                        <li>设备A 10:00:00组：MAX(220, 280, 350) = <strong>350</strong></li>
                        <li>设备B 09:00:00组：MAX(80, 120) = <strong>120</strong></li>
                    </ul>
                    <p><strong>结果</strong>：获得每个时间段的最大累计值</p>
                </div>
            </div>
            
            <div class="alert alert-success mt-3">
                <h6>🎉 结论</h6>
                <p class="mb-0">
                    我们的SQL逻辑是<strong>正确的</strong>！<code>DATE_FORMAT</code>函数将时间标准化到间隔开始时间，
                    但<code>GROUP BY</code>确保整个时间段内的所有数据都被考虑，并通过<code>MAX()</code>函数获取最大累计值。
                    这正是累计数据差值计算所需要的逻辑。
                </p>
            </div>
        </div>

        <!-- 常见误解说明 -->
        <div class="alert alert-warning mt-4">
            <h6>⚠️ 常见误解澄清</h6>
            <p><strong>误解</strong>：以为DATE_FORMAT只会取整点时刻的数据</p>
            <p><strong>实际</strong>：DATE_FORMAT只是用于分组标识，GROUP BY会聚合整个时间段内的所有数据</p>
            <p><strong>类比</strong>：就像给不同时间的苹果贴上"上午"标签，然后统计"上午"标签下苹果的总重量</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
