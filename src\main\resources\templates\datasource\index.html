<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胜大科技智联管理系统 - 数据源管理</title>
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/bootstrap-icons.min.css}">
    <link rel="stylesheet" type="text/css" th:href="@{/css/button-styles.css}">
    <script type="text/javascript" th:src="@{/js/bootstrap.bundle.min.js}"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        
        /* 导航条美化样式 */
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --transition: all 0.3s ease;
        }

        .navbar.navbar-expand-lg {
            background: var(--primary-gradient) !important;
            background-color: transparent !important;
            padding: 0.5rem 1rem !important;
            margin-bottom: 1rem !important;
            height: 56px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            border: none !important;
        }

        .navbar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand:hover {
            color: rgba(255, 255, 255, 0.9);
        }

        .navbar .user-info {
            display: flex;
            align-items: center;
        }

        .navbar .text-white {
            color: white !important;
            margin-right: 15px;
            font-weight: 500;
        }

        .navbar .btn-outline-light {
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            transition: var(--transition);
            font-weight: 500;
        }

        .navbar .btn-outline-light:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: white;
            transform: translateY(-1px);
            color: white;
        }

        .navbar .btn-outline-light.active {
            background-color: rgba(255, 255, 255, 0.3);
            border-color: white;
            color: white;
        }
        
        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .page-title {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .datasource-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin-bottom: 1rem;
            background: white;
        }

        .datasource-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .datasource-card-header {
            padding: 1rem;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .datasource-info {
            flex: 1;
        }

        .datasource-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #495057;
        }

        .datasource-description {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .datasource-meta {
            display: flex;
            gap: 1rem;
            font-size: 0.8rem;
            color: #6c757d;
        }

        .datasource-actions {
            display: flex;
            gap: 0.5rem;
        }

        .datasource-actions .btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25em 0.5em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .status-connected {
            color: #fff;
            background-color: #198754;
        }

        .status-disconnected {
            color: #fff;
            background-color: #6c757d;
        }

        .status-error {
            color: #fff;
            background-color: #dc3545;
        }

        .type-badge {
            display: inline-block;
            padding: 0.25em 0.5em;
            font-size: 0.75em;
            font-weight: 500;
            border-radius: 0.25rem;
            margin-right: 0.5rem;
        }

        .type-database {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .type-api {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }

        .type-file {
            background-color: #e8f5e8;
            color: #388e3c;
        }

        .type-mqtt {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .empty-message {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .empty-message i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .statistics-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 3rem;
        }

        /* 数据集向导样式 */
        .wizard-steps {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 1rem;
        }

        .step {
            text-align: center;
            position: relative;
            flex: 1;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background-color: #0d6efd;
            color: white;
        }

        .step.completed .step-number {
            background-color: #198754;
            color: white;
        }

        .step-title {
            font-size: 0.875rem;
            color: #6c757d;
            font-weight: 500;
        }

        .step.active .step-title {
            color: #0d6efd;
            font-weight: 600;
        }

        .step.completed .step-title {
            color: #198754;
        }

        .wizard-content {
            min-height: 300px;
            padding: 1rem 0;
        }

        .table-list, .field-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.5rem;
        }

        .table-item, .field-item {
            padding: 0.5rem;
            margin-bottom: 0.25rem;
            border-radius: 0.25rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border: 1px solid transparent;
        }

        .table-item:hover, .field-item:hover {
            background-color: #f8f9fa;
        }

        .table-item.selected, .field-item.selected {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }

        .table-item i, .field-item i {
            margin-right: 0.5rem;
            color: #6c757d;
        }

        .table-item.selected i, .field-item.selected i {
            color: #1976d2;
        }

        .field-type {
            font-size: 0.75rem;
            color: #6c757d;
            margin-left: auto;
        }

        #previewTable {
            font-size: 0.875rem;
        }

        #previewTable th {
            background-color: #f8f9fa;
            font-weight: 600;
            border-top: none;
        }

        .tab-content {
            margin-top: 1rem;
        }

        /* 字段配置样式 */
        .config-section {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            height: 300px;
            overflow-y: auto;
        }

        .field-config-container {
            height: 100%;
        }

        .role-indicator {
            font-size: 0.75rem;
            margin-bottom: 0.25rem;
            color: #6c757d;
        }

        .field-config-item {
            border: 1px solid #e9ecef;
            border-radius: 0.25rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background-color: #f8f9fa;
            transition: all 0.2s ease;
        }

        .field-config-item:hover {
            border-color: #adb5bd;
            background-color: #e9ecef;
        }

        .field-config-item.selected {
            border-color: #0d6efd;
            background-color: #e3f2fd;
        }

        .field-name {
            font-weight: 600;
            color: #495057;
        }

        .field-type-info {
            font-size: 0.75rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }

        .field-role-selector {
            margin-top: 0.5rem;
        }

        .field-role-selector .form-check {
            display: inline-block;
            margin-right: 1rem;
        }

        .field-role-selector .form-check-input:checked[value="label"] + .form-check-label {
            color: #0d6efd;
            font-weight: 600;
        }

        .field-role-selector .form-check-input:checked[value="value"] + .form-check-label {
            color: #198754;
            font-weight: 600;
        }



        /* 筛选条件构建器样式 */
        .filter-builder {
            height: 100%;
        }

        .filter-condition {
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background-color: white;
        }

        .filter-condition-header {
            display: flex;
            justify-content-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .filter-condition-content {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 0.5rem;
            align-items: center;
        }

        .logic-operator {
            text-align: center;
            font-weight: 600;
            color: #6c757d;
            padding: 0.25rem 0.5rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            margin: 0.5rem 0;
        }

        .filter-field-select,
        .filter-operator-select,
        .filter-value-input {
            font-size: 0.875rem;
        }

        .remove-condition-btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        /* SQL预览样式 */
        #customSql {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            background-color: #f8f9fa;
        }

        #customSql:focus {
            background-color: white;
        }

        .sql-status {
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }

        .sql-status.valid {
            color: #198754;
        }

        .sql-status.invalid {
            color: #dc3545;
        }

        /* 刷新按钮动画 */
        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .datasource-status {
            font-size: 0.875rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 引入导航条片段 -->
    <nav th:replace="fragments/navbar :: navbar('胜大科技智联管理系统 - 数据源管理', 'dataSource', true, true, true, null)"></nav>
    
    <div class="main-container">
        <div class="page-title">
            <h2><i class="bi bi-database"></i> 数据源管理</h2>
            <div>
                <button class="btn btn-primary" id="createDataSourceBtn">
                    <i class="bi bi-plus-circle"></i> 新建数据源
                </button>
                <button class="btn btn-success" id="createDataSetBtn" style="display: none;">
                    <i class="bi bi-plus-circle"></i> 新建数据集
                </button>
                <button class="btn btn-outline-secondary" id="refreshBtn">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 标签页导航 -->
        <ul class="nav nav-tabs" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="dataSources-tab" data-bs-toggle="tab" data-bs-target="#dataSources" type="button" role="tab">
                    <i class="bi bi-database"></i> 数据源
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dataSets-tab" data-bs-toggle="tab" data-bs-target="#dataSets" type="button" role="tab">
                    <i class="bi bi-table"></i> 数据集
                </button>
            </li>
        </ul>

        <!-- 标签页内容 -->
        <div class="tab-content" id="mainTabContent">
            <!-- 数据源标签页 -->
            <div class="tab-pane fade show active" id="dataSources" role="tabpanel">
                <!-- 统计卡片 -->
                <div class="statistics-cards" id="statisticsCards">
                    <!-- 统计信息将动态加载 -->
                </div>

                <!-- 数据源列表 -->
                <div id="dataSourceList">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据集标签页 -->
            <div class="tab-pane fade" id="dataSets" role="tabpanel">
                <!-- 数据集统计卡片 -->
                <div class="statistics-cards" id="dataSetStatisticsCards">
                    <!-- 数据集统计信息将动态加载 -->
                </div>

                <!-- 数据集列表 -->
                <div id="dataSetList">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑数据源模态框 -->
    <div class="modal fade" id="dataSourceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dataSourceModalTitle">新建数据源</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dataSourceForm">
                        <input type="hidden" id="dataSourceId">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dataSourceName" class="form-label">数据源名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="dataSourceName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dataSourceType" class="form-label">数据源类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="dataSourceType" required>
                                        <option value="">请选择数据源类型</option>
                                        <!-- 选项将动态加载 -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="dataSourceDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="dataSourceDescription" rows="2"></textarea>
                        </div>
                        
                        <!-- 数据库连接配置 -->
                        <div id="databaseConfig" class="config-section" style="display: none;">
                            <h6 class="mb-3">数据库连接配置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="dbHost" class="form-label">主机地址</label>
                                        <input type="text" class="form-control" id="dbHost" placeholder="localhost">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="dbPort" class="form-label">端口</label>
                                        <input type="number" class="form-control" id="dbPort" placeholder="3306">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="dbDatabase" class="form-label">数据库名</label>
                                        <input type="text" class="form-control" id="dbDatabase">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="dbUsername" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="dbUsername">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="dbPassword" class="form-label">密码</label>
                                        <input type="password" class="form-control" id="dbPassword">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="dbDriverType" class="form-label">数据库类型</label>
                                <select class="form-select" id="dbDriverType">
                                    <option value="mysql">MySQL</option>
                                    <option value="postgresql">PostgreSQL</option>
                                    <option value="sqlserver">SQL Server</option>
                                    <option value="oracle">Oracle</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="dataSourceEnabled" checked>
                                <label class="form-check-label" for="dataSourceEnabled">
                                    启用数据源
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" id="testConnectionBtn">
                        <i class="bi bi-wifi"></i> 测试连接
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveDataSourceBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据集创建向导模态框 -->
    <div class="modal fade" id="dataSetWizardModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">数据集创建向导</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- 步骤指示器 -->
                    <div class="wizard-steps mb-4">
                        <div class="d-flex justify-content-between">
                            <div class="step active" data-step="1">
                                <div class="step-number">1</div>
                                <div class="step-title">选择数据源</div>
                            </div>
                            <div class="step" data-step="2">
                                <div class="step-number">2</div>
                                <div class="step-title">选择表和字段</div>
                            </div>
                            <div class="step" data-step="3">
                                <div class="step-number">3</div>
                                <div class="step-title">配置映射</div>
                            </div>
                            <div class="step" data-step="4">
                                <div class="step-number">4</div>
                                <div class="step-title">预览和保存</div>
                            </div>
                        </div>
                    </div>

                    <!-- 步骤内容 -->
                    <div class="wizard-content">
                        <!-- 步骤1：选择数据源 -->
                        <div class="wizard-step" id="wizardStep1">
                            <h6>选择数据源</h6>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <label for="wizardDataSource" class="form-label mb-0">数据源 <span class="text-danger">*</span></label>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="refreshDataSourcesBtn" title="刷新数据源列表">
                                                <i class="bi bi-arrow-clockwise"></i> 刷新
                                            </button>
                                        </div>
                                        <select class="form-select" id="wizardDataSource" required>
                                            <option value="">请选择数据源</option>
                                            <!-- 选项将动态加载 -->
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤2：可视化字段配置 -->
                        <div class="wizard-step" id="wizardStep2" style="display: none;">
                            <h6>配置数据字段和筛选条件</h6>

                            <!-- 主配置区域 -->
                            <div class="row mb-4">
                                <!-- 表选择 -->
                                <div class="col-md-3">
                                    <div class="config-section">
                                        <label class="form-label fw-bold">数据表</label>
                                        <div class="table-list" id="tableList">
                                            <!-- 表列表将动态加载 -->
                                        </div>
                                    </div>
                                </div>

                                <!-- 字段配置 -->
                                <div class="col-md-4">
                                    <div class="config-section">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <label class="form-label fw-bold mb-0">字段配置</label>
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="resetFieldsBtn" title="重置所有字段选择">
                                                <i class="bi bi-arrow-clockwise"></i> 重置
                                            </button>
                                        </div>
                                        <div class="field-config-container">
                                            <div class="field-roles mb-3">
                                                <div class="role-indicator">
                                                    <span class="badge bg-primary me-2">标签</span>用于图表分类/X轴
                                                </div>
                                                <div class="role-indicator">
                                                    <span class="badge bg-success me-2">数值</span>用于图表数据/Y轴
                                                </div>
                                            </div>
                                            <div class="field-config-list" id="fieldConfigList">
                                                <!-- 字段配置列表将动态加载 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 筛选条件构建器 -->
                                <div class="col-md-5">
                                    <div class="config-section">
                                        <label class="form-label fw-bold">筛选条件</label>
                                        <div class="filter-builder">
                                            <div class="filter-conditions" id="filterConditions">
                                                <!-- 筛选条件将动态加载 -->
                                            </div>
                                            <button type="button" class="btn btn-outline-primary btn-sm" id="addFilterBtn">
                                                <i class="bi bi-plus"></i> 添加条件
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二行：输出限制、日期格式化和聚合配置 -->
                            <div class="row mb-4">
                                <!-- 输出限制配置 -->
                                <div class="col-md-3">
                                    <div class="config-section">
                                        <label class="form-label fw-bold">输出限制</label>
                                        <div class="mb-2">
                                            <input type="number" class="form-control form-control-sm" id="outputLimit"
                                                   placeholder="最大记录数" min="1" max="10000">
                                            <div class="form-text small">限制查询结果的最大记录数，提高查询性能</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 日期格式化配置 -->
                                <div class="col-md-4">
                                    <div class="config-section">
                                        <label class="form-label fw-bold">日期格式化</label>
                                        <div class="mb-2">
                                            <select class="form-select form-select-sm" id="dateField">
                                                <option value="">选择日期字段</option>
                                                <!-- 日期字段选项将动态加载 -->
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <select class="form-select form-select-sm" id="dateFormat">
                                                <option value="">不格式化</option>
                                                <option value="%Y-%m-%d %H:%i:%s">年月日时分秒 (2025-07-22 17:35:55)</option>
                                                <option value="%m-%d %H:%i:%s">月日时分秒 (07-22 17:35:55)</option>
                                                <option value="%m-%d %H:%i">月日时分 (07-22 17:35)</option>
                                                <option value="%m-%d">月日 (07-22)</option>
                                                <option value="%H:%i:%s">时分秒 (17:35:55)</option>
                                                <option value="%H:%i">时分 (17:35)</option>
                                            </select>
                                            <div class="form-text small">对日期字段进行SQL格式化</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 聚合配置 -->
                                <div class="col-md-5">
                                    <div class="config-section">
                                        <label class="form-label fw-bold">聚合配置</label>
                                        <div class="aggregation-config">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="enableAggregation">
                                                <label class="form-check-label" for="enableAggregation">
                                                    启用聚合查询
                                                </label>
                                            </div>
                                            <div id="aggregationOptions" style="display: none;">
                                                <div class="mb-2">
                                                    <label for="aggregationType" class="form-label form-label-sm">聚合类型</label>
                                                    <select class="form-select form-select-sm" id="aggregationType">
                                                        <option value="MAX">最大值</option>
                                                        <option value="MIN">最小值</option>
                                                        <option value="AVG">平均值</option>
                                                        <option value="SUM">求和</option>
                                                        <option value="COUNT">计数</option>
                                                        <option value="HOURLY_DIFF">每小时产能差值</option>
                                                    </select>
                                                </div>
                                                <div class="mb-2">
                                                    <label for="timeField" class="form-label form-label-sm">时间字段</label>
                                                    <select class="form-select form-select-sm" id="timeField">
                                                        <option value="">选择时间字段（用于排序）</option>
                                                        <!-- 时间字段选项将动态加载 -->
                                                    </select>
                                                    <div class="form-text small">当有多个相同聚合值时，按此字段排序取最新记录</div>
                                                </div>
                                                <!-- 差值计算配置 -->
                                                <div id="diffCalculationOptions" style="display: none;">
                                                    <div class="mb-2">
                                                        <label for="timeInterval" class="form-label form-label-sm">时间间隔</label>
                                                        <select class="form-select form-select-sm" id="timeInterval">
                                                            <option value="HOUR">小时</option>
                                                            <option value="DAY">天</option>
                                                            <option value="WEEK">周</option>
                                                            <option value="MONTH">月</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="ignoreNegative" checked>
                                                            <label class="form-check-label" for="ignoreNegative">
                                                                忽略负值（当累计值重置时）
                                                            </label>
                                                        </div>
                                                        <div class="form-text small">当累计值重置或出现异常负值时，将其设为0</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- SQL预览区域 -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="config-section">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <label class="form-label fw-bold mb-0">SQL预览</label>
                                            <div>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" id="refreshSqlBtn">
                                                    <i class="bi bi-arrow-clockwise"></i> 重新生成
                                                </button>
                                                <button type="button" class="btn btn-outline-info btn-sm" id="validateSqlBtn">
                                                    <i class="bi bi-check-circle"></i> 验证SQL
                                                </button>
                                            </div>
                                        </div>
                                        <textarea class="form-control" id="customSql" rows="4" placeholder="SQL将根据您的配置自动生成..."></textarea>
                                        <div class="form-text">SQL会根据字段配置和筛选条件自动生成，您也可以手动编辑</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤3：配置映射 -->
                        <div class="wizard-step" id="wizardStep3" style="display: none;">
                            <h6>配置字段映射</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="labelField" class="form-label">标签字段 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="labelField" required>
                                            <option value="">请选择标签字段</option>
                                            <!-- 字段选项将动态加载 -->
                                        </select>
                                        <div class="form-text">用于图表的X轴标签或分类</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="valueField" class="form-label">数值字段 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="valueField" required>
                                            <option value="">请选择数值字段</option>
                                            <!-- 字段选项将动态加载 -->
                                        </select>
                                        <div class="form-text">用于图表的Y轴数值或数据值</div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="dataSetName" class="form-label">数据集名称 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="dataSetName" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="dataSetDescription" class="form-label">数据集描述</label>
                                        <textarea class="form-control" id="dataSetDescription" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤4：预览和保存 -->
                        <div class="wizard-step" id="wizardStep4" style="display: none;">
                            <h6>数据预览</h6>
                            <div class="mb-3">
                                <button type="button" class="btn btn-outline-primary" id="previewDataBtn">
                                    <i class="bi bi-eye"></i> 预览数据
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped" id="previewTable">
                                    <thead id="previewTableHead">
                                        <!-- 表头将动态生成 -->
                                    </thead>
                                    <tbody id="previewTableBody">
                                        <!-- 数据将动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="wizardPrevBtn" style="display: none;">上一步</button>
                    <button type="button" class="btn btn-primary" id="wizardNextBtn">下一步</button>
                    <button type="button" class="btn btn-success" id="wizardSaveBtn" style="display: none;">保存数据集</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/datasource-manager.js}"></script>
    <script th:src="@{/js/dataset-field-configurator.js}"></script>
    <script th:src="@{/js/dataset-wizard.js}"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof DataSourceManager !== 'undefined') {
                window.dataSourceManager = new DataSourceManager();
                window.dataSourceManager.init();
            }

            if (typeof DataSetWizard !== 'undefined') {
                window.dataSetWizard = new DataSetWizard();
                window.dataSetWizard.init();
            }

            if (typeof DataSetFieldConfigurator !== 'undefined') {
                window.fieldConfigurator = new DataSetFieldConfigurator();
                window.fieldConfigurator.init();
            }
        });
    </script>
</body>
</html>
