<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>每小时产能差值计算测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .config-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .field-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
        }
        .field-name {
            font-weight: bold;
            color: #333;
        }
        .field-type-info {
            font-size: 0.85em;
            color: #666;
        }
        .sql-preview {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-result {
            background: #f0f8ff;
            border: 1px solid #b0d4f1;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        .error {
            background: #ffe6e6;
            border: 1px solid #ff9999;
            color: #cc0000;
        }
        .success {
            background: #e6ffe6;
            border: 1px solid #99ff99;
            color: #006600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="mb-4">每小时产能差值计算功能测试</h1>
        
        <div class="row">
            <!-- 字段配置区域 -->
            <div class="col-md-6">
                <div class="config-section">
                    <label class="form-label fw-bold">字段配置</label>
                    <div id="fieldsContainer">
                        <!-- 模拟数据历史表字段 -->
                        <div class="field-item">
                            <div class="field-name">device_name</div>
                            <div class="field-type-info">VARCHAR(100) - 设备名称</div>
                            <div class="field-role-selector mt-2">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" data-field="device_name" data-role="label" id="label_device_name">
                                    <label class="form-check-label" for="label_device_name">标签</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" data-field="device_name" data-role="value" id="value_device_name">
                                    <label class="form-check-label" for="value_device_name">数值</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="field-item">
                            <div class="field-name">value</div>
                            <div class="field-type-info">INT - 累计产能值</div>
                            <div class="field-role-selector mt-2">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" data-field="value" data-role="label" id="label_value">
                                    <label class="form-check-label" for="label_value">标签</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" data-field="value" data-role="value" id="value_value" checked>
                                    <label class="form-check-label" for="value_value">数值</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="field-item">
                            <div class="field-name">timestamp</div>
                            <div class="field-type-info">DATETIME - 时间戳</div>
                            <div class="field-role-selector mt-2">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" data-field="timestamp" data-role="label" id="label_timestamp">
                                    <label class="form-check-label" for="label_timestamp">标签</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="checkbox" data-field="timestamp" data-role="value" id="value_timestamp">
                                    <label class="form-check-label" for="value_timestamp">数值</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 聚合配置区域 -->
            <div class="col-md-6">
                <div class="config-section">
                    <label class="form-label fw-bold">聚合配置</label>
                    <div class="aggregation-config">
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="enableAggregation" checked>
                            <label class="form-check-label" for="enableAggregation">
                                启用聚合查询
                            </label>
                        </div>
                        <div id="aggregationOptions">
                            <div class="mb-2">
                                <label for="aggregationType" class="form-label form-label-sm">聚合类型</label>
                                <select class="form-select form-select-sm" id="aggregationType">
                                    <option value="MAX">最大值</option>
                                    <option value="MIN">最小值</option>
                                    <option value="AVG">平均值</option>
                                    <option value="SUM">求和</option>
                                    <option value="COUNT">计数</option>
                                    <option value="HOURLY_DIFF" selected>每小时产能差值</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <label for="timeField" class="form-label form-label-sm">时间字段</label>
                                <select class="form-select form-select-sm" id="timeField">
                                    <option value="">选择时间字段</option>
                                    <option value="timestamp" selected>timestamp</option>
                                </select>
                            </div>
                            <!-- 差值计算配置 -->
                            <div id="diffCalculationOptions">
                                <div class="mb-2">
                                    <label for="timeInterval" class="form-label form-label-sm">时间间隔</label>
                                    <select class="form-select form-select-sm" id="timeInterval">
                                        <option value="HOUR" selected>小时</option>
                                        <option value="DAY">天</option>
                                        <option value="WEEK">周</option>
                                        <option value="MONTH">月</option>
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="ignoreNegative" checked>
                                        <label class="form-check-label" for="ignoreNegative">
                                            忽略负值（当累计值重置时）
                                        </label>
                                    </div>
                                    <div class="form-text small">当累计值重置或出现异常负值时，将其设为0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SQL预览区域 -->
        <div class="config-section">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <label class="form-label fw-bold mb-0">SQL预览</label>
                <button class="btn btn-primary btn-sm" onclick="generateTestSQL()">生成SQL</button>
            </div>
            <div class="sql-preview" id="sqlPreview">
                点击"生成SQL"按钮查看生成的SQL语句
            </div>
        </div>

        <!-- 测试结果区域 -->
        <div id="testResult" class="test-result" style="display: none;">
            <h5>测试结果</h5>
            <div id="testResultContent"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟数据集字段配置器的核心功能
        class TestDataSetFieldConfigurator {
            constructor() {
                this.fieldRoles = {
                    'device_name': 'label',
                    'value': 'value'
                };
                this.selectedTable = { name: 'data_history' };
                this.aggregationConfig = {
                    enabled: true,
                    type: 'HOURLY_DIFF',
                    timeField: 'timestamp',
                    timeInterval: 'HOUR',
                    ignoreNegative: true
                };
            }

            generateHourlyDiffSQL(labelField, valueField, timeField, whereClause) {
                // 输入验证
                if (!timeField) {
                    throw new Error('差值计算需要选择时间字段');
                }
                if (!labelField) {
                    throw new Error('差值计算需要选择标签字段');
                }
                if (!valueField) {
                    throw new Error('差值计算需要选择数值字段');
                }

                const timeInterval = this.aggregationConfig.timeInterval || 'HOUR';
                const ignoreNegative = this.aggregationConfig.ignoreNegative !== false;

                // 根据时间间隔生成时间格式化字符串
                let timeFormat;
                switch (timeInterval) {
                    case 'HOUR':
                        timeFormat = '%Y-%m-%d %H:00:00';
                        break;
                    case 'DAY':
                        timeFormat = '%Y-%m-%d 00:00:00';
                        break;
                    case 'WEEK':
                        timeFormat = '%Y-%u周';
                        break;
                    case 'MONTH':
                        timeFormat = '%Y-%m-01 00:00:00';
                        break;
                    default:
                        timeFormat = '%Y-%m-%d %H:00:00';
                }

                // 构建CTE查询
                let sql = `WITH interval_max AS (\n`;
                sql += `    SELECT \n`;
                sql += `        ${labelField},\n`;
                sql += `        DATE_FORMAT(${timeField}, '${timeFormat}') as time_interval,\n`;
                sql += `        MAX(${valueField}) as max_value\n`;
                sql += `    FROM ${this.selectedTable.name}\n`;
                
                if (whereClause) {
                    sql += `    ${whereClause}\n`;
                }
                
                sql += `    GROUP BY ${labelField}, DATE_FORMAT(${timeField}, '${timeFormat}')\n`;
                sql += `),\n`;
                sql += `interval_diff AS (\n`;
                sql += `    SELECT \n`;
                sql += `        ${labelField},\n`;
                sql += `        time_interval,\n`;
                sql += `        max_value,\n`;
                sql += `        LAG(max_value, 1, 0) OVER (\n`;
                sql += `            PARTITION BY ${labelField} \n`;
                sql += `            ORDER BY time_interval\n`;
                sql += `        ) as prev_value,\n`;
                sql += `        max_value - LAG(max_value, 1, 0) OVER (\n`;
                sql += `            PARTITION BY ${labelField} \n`;
                sql += `            ORDER BY time_interval\n`;
                sql += `        ) as interval_output\n`;
                sql += `    FROM interval_max\n`;
                sql += `)\n`;
                sql += `SELECT \n`;
                sql += `    ${labelField},\n`;
                sql += `    time_interval,\n`;
                
                if (ignoreNegative) {
                    sql += `    CASE WHEN interval_output < 0 THEN 0 ELSE interval_output END as interval_output\n`;
                } else {
                    sql += `    interval_output\n`;
                }
                
                sql += `FROM interval_diff\n`;
                sql += `WHERE interval_output IS NOT NULL\n`;
                sql += `ORDER BY ${labelField}, time_interval DESC`;

                return sql;
            }
        }

        const configurator = new TestDataSetFieldConfigurator();

        function generateTestSQL() {
            const resultDiv = document.getElementById('testResult');
            const resultContent = document.getElementById('testResultContent');
            const sqlPreview = document.getElementById('sqlPreview');
            
            try {
                // 更新配置
                configurator.aggregationConfig.type = document.getElementById('aggregationType').value;
                configurator.aggregationConfig.timeField = document.getElementById('timeField').value;
                configurator.aggregationConfig.timeInterval = document.getElementById('timeInterval').value;
                configurator.aggregationConfig.ignoreNegative = document.getElementById('ignoreNegative').checked;
                
                if (configurator.aggregationConfig.type === 'HOURLY_DIFF') {
                    const sql = configurator.generateHourlyDiffSQL('device_name', 'value', 'timestamp', null);
                    sqlPreview.textContent = sql;
                    
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'test-result success';
                    resultContent.innerHTML = `
                        <p><strong>✅ SQL生成成功！</strong></p>
                        <p>生成的SQL包含以下特性：</p>
                        <ul>
                            <li>使用CTE（公用表表达式）进行复杂查询</li>
                            <li>按${configurator.aggregationConfig.timeInterval}分组聚合</li>
                            <li>使用LAG窗口函数计算前一时间段的值</li>
                            <li>计算每个时间段的产能差值</li>
                            <li>${configurator.aggregationConfig.ignoreNegative ? '忽略负值' : '保留负值'}</li>
                        </ul>
                        <div class="alert alert-info mt-3">
                            <h6>📋 时间聚合逻辑说明：</h6>
                            <p><strong>示例</strong>：对于小时间隔聚合</p>
                            <ul class="mb-0">
                                <li>09:15:23 的记录值=100 → 归入 09:00:00 时间段</li>
                                <li>09:32:45 的记录值=150 → 归入 09:00:00 时间段</li>
                                <li>09:58:12 的记录值=200 → 归入 09:00:00 时间段</li>
                                <li><strong>结果</strong>：09:00:00 时间段的最大值 = 200</li>
                            </ul>
                            <p class="mt-2 mb-0"><small>这确保了整个时间段内的所有数据都被考虑，取其中的最大累计值。</small></p>
                        </div>
                    `;
                } else {
                    sqlPreview.textContent = '请选择"每小时产能差值"聚合类型进行测试';
                    resultDiv.style.display = 'none';
                }
            } catch (error) {
                sqlPreview.textContent = `错误: ${error.message}`;
                resultDiv.style.display = 'block';
                resultDiv.className = 'test-result error';
                resultContent.innerHTML = `<p><strong>❌ 测试失败：</strong>${error.message}</p>`;
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示差值计算选项
            document.getElementById('diffCalculationOptions').style.display = 'block';
            
            // 绑定事件
            document.getElementById('aggregationType').addEventListener('change', function() {
                const isDiff = this.value === 'HOURLY_DIFF';
                document.getElementById('diffCalculationOptions').style.display = isDiff ? 'block' : 'none';
            });
            
            // 自动生成初始SQL
            generateTestSQL();
        });
    </script>
</body>
</html>
