/**
 * 数据集字段配置器
 * 负责可视化的字段配置和筛选条件构建
 */
class DataSetFieldConfigurator {
    constructor() {
        this.fields = [];
        this.fieldRoles = {}; // {fieldName: role}
        this.filterConditions = [];
        this.selectedTable = null;
        this.aggregationConfig = {
            enabled: false,
            type: 'MAX', // MAX, MIN, AVG, SUM, COUNT, HOURLY_DIFF
            timeField: '', // 用于排序的时间字段
            groupByLabel: true, // 是否按标签字段分组
            timeInterval: 'HOUR', // 时间间隔：HOUR, DAY, WEEK, MONTH
            ignoreNegative: true // 是否忽略负值
        };
    }

    /**
     * 初始化
     */
    init() {
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 添加筛选条件按钮
        document.getElementById('addFilterBtn').addEventListener('click', () => {
            this.addFilterCondition();
        });

        // 重新生成SQL按钮
        document.getElementById('refreshSqlBtn').addEventListener('click', () => {
            this.generateSQL();
        });

        // 验证SQL按钮
        document.getElementById('validateSqlBtn').addEventListener('click', () => {
            this.validateSQL();
        });

        // 重置字段选择按钮
        document.getElementById('resetFieldsBtn').addEventListener('click', () => {
            this.resetFieldSelections();
        });

        // 聚合配置事件
        this.bindAggregationEvents();

        // 输出限制事件
        this.bindOutputLimitEvents();

        // 日期格式化事件
        this.bindDateFormatEvents();
    }

    /**
     * 绑定聚合配置事件
     */
    bindAggregationEvents() {
        // 启用聚合开关
        const enableAggregation = document.getElementById('enableAggregation');
        if (enableAggregation) {
            enableAggregation.addEventListener('change', (e) => {
                this.aggregationConfig.enabled = e.target.checked;
                this.toggleAggregationOptions(e.target.checked);
                this.generateSQL();
            });
        }

        // 聚合类型选择
        const aggregationType = document.getElementById('aggregationType');
        if (aggregationType) {
            aggregationType.addEventListener('change', (e) => {
                this.aggregationConfig.type = e.target.value;
                this.toggleDiffCalculationOptions(e.target.value === 'HOURLY_DIFF');
                this.generateSQL();
            });
        }

        // 时间字段选择
        const timeField = document.getElementById('timeField');
        if (timeField) {
            timeField.addEventListener('change', (e) => {
                this.aggregationConfig.timeField = e.target.value;
                this.generateSQL();
            });
        }

        // 时间间隔选择
        const timeInterval = document.getElementById('timeInterval');
        if (timeInterval) {
            timeInterval.addEventListener('change', (e) => {
                this.aggregationConfig.timeInterval = e.target.value;
                this.generateSQL();
            });
        }

        // 忽略负值选项
        const ignoreNegative = document.getElementById('ignoreNegative');
        if (ignoreNegative) {
            ignoreNegative.addEventListener('change', (e) => {
                this.aggregationConfig.ignoreNegative = e.target.checked;
                this.generateSQL();
            });
        }
    }

    /**
     * 绑定输出限制事件
     */
    bindOutputLimitEvents() {
        const outputLimit = document.getElementById('outputLimit');
        if (outputLimit) {
            outputLimit.addEventListener('input', () => {
                this.generateSQL();
            });
        }
    }

    /**
     * 绑定日期格式化事件
     */
    bindDateFormatEvents() {
        const dateField = document.getElementById('dateField');
        const dateFormat = document.getElementById('dateFormat');

        if (dateField) {
            dateField.addEventListener('change', () => {
                this.generateSQL();
            });
        }

        if (dateFormat) {
            dateFormat.addEventListener('change', () => {
                this.generateSQL();
            });
        }
    }

    /**
     * 切换聚合选项显示
     */
    toggleAggregationOptions(show) {
        const aggregationOptions = document.getElementById('aggregationOptions');
        if (aggregationOptions) {
            aggregationOptions.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * 切换差值计算选项显示
     */
    toggleDiffCalculationOptions(show) {
        const diffCalculationOptions = document.getElementById('diffCalculationOptions');
        if (diffCalculationOptions) {
            diffCalculationOptions.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * 设置表和字段信息
     */
    setTableAndFields(table, fields) {
        this.selectedTable = table;
        this.fields = fields;
        this.fieldRoles = {};
        this.filterConditions = [];

        this.renderFieldConfiguration();
        this.clearFilterConditions();
        this.updateTimeFieldOptions();
        this.updateDateFieldOptions();
        this.generateSQL();
    }

    /**
     * 更新时间字段选项
     */
    updateTimeFieldOptions() {
        const timeFieldSelect = document.getElementById('timeField');
        if (!timeFieldSelect || !this.fields) return;

        // 清空现有选项
        timeFieldSelect.innerHTML = '<option value="">选择时间字段（用于排序）</option>';

        // 添加日期时间字段选项
        this.fields.forEach(field => {
            if (this.isDateTimeField(field.name)) {
                const option = document.createElement('option');
                option.value = field.name;
                option.textContent = `${field.name} (${field.type})`;
                timeFieldSelect.appendChild(option);
            }
        });
    }

    /**
     * 更新日期字段选项
     */
    updateDateFieldOptions() {
        const dateFieldSelect = document.getElementById('dateField');
        if (!dateFieldSelect || !this.fields) return;

        // 清空现有选项
        dateFieldSelect.innerHTML = '<option value="">选择日期字段</option>';

        // 添加日期时间字段选项
        this.fields.forEach(field => {
            if (this.isDateTimeField(field.name)) {
                const option = document.createElement('option');
                option.value = field.name;
                option.textContent = `${field.name} (${field.type})`;
                dateFieldSelect.appendChild(option);
            }
        });
    }

    /**
     * 渲染字段配置列表
     */
    renderFieldConfiguration() {
        const container = document.getElementById('fieldConfigList');
        container.innerHTML = '';

        if (this.fields.length === 0) {
            container.innerHTML = '<div class="text-muted">请先选择数据表</div>';
            return;
        }

        this.fields.forEach(field => {
            const fieldItem = this.createFieldConfigItem(field);
            container.appendChild(fieldItem);
        });
    }

    /**
     * 创建字段配置项
     */
    createFieldConfigItem(field) {
        const fieldItem = document.createElement('div');
        fieldItem.className = 'field-config-item';
        fieldItem.dataset.fieldName = field.name;

        fieldItem.innerHTML = `
            <div class="field-name">${field.name}</div>
            <div class="field-type-info">${field.type}${field.size ? `(${field.size})` : ''}</div>
            ${field.remarks ? `<div class="field-type-info">${field.remarks}</div>` : ''}
            
            <div class="field-role-selector">
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="checkbox" data-field="${field.name}" data-role="label" id="label_${field.name}">
                    <label class="form-check-label" for="label_${field.name}">标签</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="checkbox" data-field="${field.name}" data-role="value" id="value_${field.name}">
                    <label class="form-check-label" for="value_${field.name}">数值</label>
                </div>
            </div>
        `;

        // 绑定角色选择事件
        fieldItem.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('click', (e) => {
                const fieldName = e.target.dataset.field;
                const role = e.target.dataset.role;
                const isChecked = e.target.checked;

                this.handleFieldSelection(fieldName, role, isChecked);
            });
        });

        return fieldItem;
    }

    /**
     * 处理字段选择
     */
    handleFieldSelection(fieldName, role, isChecked) {
        if (isChecked) {
            // 选中：设置字段角色，并清除同类型其他字段
            this.setFieldRole(fieldName, role);
        } else {
            // 取消选中：移除字段角色
            this.clearFieldRole(fieldName);
        }
    }

    /**
     * 设置字段角色
     */
    setFieldRole(fieldName, role) {
        // 只处理标签和数值角色，清除其他字段的相同角色
        if (role === 'label' || role === 'value') {
            Object.keys(this.fieldRoles).forEach(key => {
                if (this.fieldRoles[key] === role && key !== fieldName) {
                    delete this.fieldRoles[key];
                    // 清除UI中的选择
                    const otherCheckbox = document.querySelector(`input[data-field="${key}"][data-role="${role}"]`);
                    if (otherCheckbox) {
                        otherCheckbox.checked = false;
                    }
                    // 更新其他字段的外观
                    this.updateFieldItemAppearance(key, null);
                }
            });

            this.fieldRoles[fieldName] = role;
            this.updateFieldItemAppearance(fieldName, role);
        }

        this.generateSQL();
        console.log('字段角色更新:', this.fieldRoles);
    }

    /**
     * 清除字段角色
     */
    clearFieldRole(fieldName) {
        if (this.fieldRoles[fieldName]) {
            delete this.fieldRoles[fieldName];
            this.updateFieldItemAppearance(fieldName, null);
            this.generateSQL();
            console.log('清除字段角色:', fieldName);
        }
    }

    /**
     * 更新字段项外观
     */
    updateFieldItemAppearance(fieldName, role) {
        const fieldItem = document.querySelector(`[data-field-name="${fieldName}"]`);
        if (fieldItem) {
            fieldItem.classList.remove('selected');
            if (role) {
                fieldItem.classList.add('selected');
            }

            // 更新checkbox状态以保持同步
            const labelCheckbox = fieldItem.querySelector(`input[data-role="label"]`);
            const valueCheckbox = fieldItem.querySelector(`input[data-role="value"]`);

            if (labelCheckbox) {
                labelCheckbox.checked = (role === 'label');
            }
            if (valueCheckbox) {
                valueCheckbox.checked = (role === 'value');
            }
        }
    }

    /**
     * 添加筛选条件
     */
    addFilterCondition() {
        const condition = {
            id: Date.now(),
            field: '',
            operator: '=',
            value: '',
            logicOperator: this.filterConditions.length > 0 ? 'AND' : null
        };

        this.filterConditions.push(condition);
        this.renderFilterConditions();
    }

    /**
     * 移除筛选条件
     */
    removeFilterCondition(conditionId) {
        this.filterConditions = this.filterConditions.filter(c => c.id !== conditionId);
        
        // 如果移除的是第一个条件，清除下一个条件的逻辑操作符
        if (this.filterConditions.length > 0) {
            this.filterConditions[0].logicOperator = null;
        }
        
        this.renderFilterConditions();
        this.generateSQL();
    }

    /**
     * 渲染筛选条件
     */
    renderFilterConditions() {
        const container = document.getElementById('filterConditions');
        container.innerHTML = '';

        this.filterConditions.forEach((condition, index) => {
            const conditionElement = this.createFilterConditionElement(condition, index);
            container.appendChild(conditionElement);
        });
    }

    /**
     * 创建筛选条件元素
     */
    createFilterConditionElement(condition, index) {
        const conditionDiv = document.createElement('div');
        conditionDiv.className = 'filter-condition';

        // 逻辑操作符
        const logicOperatorHtml = condition.logicOperator ? 
            `<div class="logic-operator">${condition.logicOperator}</div>` : '';

        conditionDiv.innerHTML = `
            ${logicOperatorHtml}
            <div class="filter-condition-header">
                <span class="text-muted">条件 ${index + 1}</span>
                <button type="button" class="btn btn-outline-danger btn-sm remove-condition-btn" onclick="fieldConfigurator.removeFilterCondition(${condition.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
            <div class="filter-condition-content">
                <select class="form-select form-select-sm filter-field-select" data-condition-id="${condition.id}" data-field="field">
                    <option value="">选择字段</option>
                    ${this.getFilterFieldOptions(condition.field)}
                </select>
                <select class="form-select form-select-sm filter-operator-select" data-condition-id="${condition.id}" data-field="operator">
                    ${this.getOperatorOptions(condition.operator, condition.field)}
                </select>
                <div class="filter-value-container" data-condition-id="${condition.id}">
                    ${this.createValueInput(condition)}
                </div>
            </div>
        `;

        // 绑定事件
        this.bindConditionEvents(conditionDiv, condition);

        // 如果不是第一个条件，添加逻辑操作符选择
        if (index > 0) {
            const logicSelect = document.createElement('select');
            logicSelect.className = 'form-select form-select-sm mb-2';
            logicSelect.innerHTML = `
                <option value="AND" ${condition.logicOperator === 'AND' ? 'selected' : ''}>AND</option>
                <option value="OR" ${condition.logicOperator === 'OR' ? 'selected' : ''}>OR</option>
            `;
            logicSelect.addEventListener('change', (e) => {
                this.updateFilterCondition(condition.id, 'logicOperator', e.target.value);
            });
            
            conditionDiv.insertBefore(logicSelect, conditionDiv.firstChild);
        }

        return conditionDiv;
    }

    /**
     * 获取筛选字段选项
     */
    getFilterFieldOptions(selectedField) {
        return this.fields.map(field => 
            `<option value="${field.name}" ${field.name === selectedField ? 'selected' : ''}>${field.name} (${field.type})</option>`
        ).join('');
    }

    /**
     * 获取操作符选项
     */
    getOperatorOptions(selectedOperator, fieldName = '') {
        const operators = [
            { value: '=', label: '等于 (=)' },
            { value: '!=', label: '不等于 (!=)' },
            { value: '>', label: '大于 (>)' },
            { value: '>=', label: '大于等于 (>=)' },
            { value: '<', label: '小于 (<)' },
            { value: '<=', label: '小于等于 (<=)' },
            { value: 'LIKE', label: '包含 (LIKE)' },
            { value: 'NOT LIKE', label: '不包含 (NOT LIKE)' },
            { value: 'IN', label: '在列表中 (IN)' },
            { value: 'NOT IN', label: '不在列表中 (NOT IN)' },
            { value: 'IS NULL', label: '为空 (IS NULL)' },
            { value: 'IS NOT NULL', label: '不为空 (IS NOT NULL)' }
        ];

        // 如果是日期时间字段，添加时间相关操作符
        if (this.isDateTimeField(fieldName)) {
            operators.push(
                { value: 'TODAY', label: '当天' },
                { value: 'THIS_WEEK', label: '本周' },
                { value: 'THIS_MONTH', label: '本月' },
                { value: 'THIS_QUARTER', label: '本季' },
                { value: 'THIS_YEAR', label: '本年' },
                { value: 'CUSTOM_DATE', label: '自定义日期' },
                { value: 'DATE_RANGE', label: '日期范围' }
            );
        }

        return operators.map(op =>
            `<option value="${op.value}" ${op.value === selectedOperator ? 'selected' : ''}>${op.label}</option>`
        ).join('');
    }

    /**
     * 检测字段是否为日期时间类型
     */
    isDateTimeField(fieldName) {
        if (!fieldName || !this.fields) return false;

        const field = this.fields.find(f => f.name === fieldName);
        if (!field) return false;

        const dateTimeTypes = ['DATE', 'DATETIME', 'TIMESTAMP', 'TIME'];
        return dateTimeTypes.some(type =>
            field.type.toUpperCase().includes(type)
        );
    }

    /**
     * 根据操作符类型创建值输入控件
     */
    createValueInput(condition) {
        const { operator, value, id } = condition;

        // 时间预设选项，不需要输入值
        const noValueOperators = ['TODAY', 'THIS_WEEK', 'THIS_MONTH', 'THIS_QUARTER', 'THIS_YEAR'];
        if (noValueOperators.includes(operator)) {
            return '<span class="text-muted small">无需输入值</span>';
        }

        // 日期范围选择
        if (operator === 'DATE_RANGE') {
            const values = value ? value.split(',') : ['', ''];
            return `
                <div class="d-flex gap-2 align-items-center">
                    <input type="date" class="form-control form-control-sm"
                           data-condition-id="${id}" data-field="startDate"
                           value="${values[0] || ''}" placeholder="开始日期">
                    <span class="text-muted">至</span>
                    <input type="date" class="form-control form-control-sm"
                           data-condition-id="${id}" data-field="endDate"
                           value="${values[1] || ''}" placeholder="结束日期">
                </div>
            `;
        }

        // 自定义日期选择
        if (operator === 'CUSTOM_DATE') {
            return `
                <input type="date" class="form-control form-control-sm filter-value-input"
                       data-condition-id="${id}" data-field="value"
                       value="${value}" placeholder="选择日期">
            `;
        }

        // 默认文本输入
        return `
            <input type="text" class="form-control form-control-sm filter-value-input"
                   data-condition-id="${id}" data-field="value"
                   value="${value}" placeholder="输入值">
        `;
    }

    /**
     * 绑定筛选条件事件
     */
    bindConditionEvents(conditionDiv, condition) {
        // 字段选择事件
        const fieldSelect = conditionDiv.querySelector('.filter-field-select');
        if (fieldSelect) {
            fieldSelect.addEventListener('change', (e) => {
                this.updateFilterCondition(condition.id, 'field', e.target.value);
                // 字段变更时，重新生成操作符选项
                this.updateOperatorOptions(condition.id, e.target.value);
            });
        }

        // 操作符选择事件
        const operatorSelect = conditionDiv.querySelector('.filter-operator-select');
        if (operatorSelect) {
            operatorSelect.addEventListener('change', (e) => {
                this.updateFilterCondition(condition.id, 'operator', e.target.value);
                // 操作符变更时，重新生成值输入控件
                this.updateValueInput(condition.id, e.target.value);
            });
        }

        // 值输入事件
        this.bindValueInputEvents(conditionDiv, condition.id);
    }

    /**
     * 绑定值输入控件事件
     */
    bindValueInputEvents(conditionDiv, conditionId) {
        // 普通输入框
        const valueInputs = conditionDiv.querySelectorAll('.filter-value-input');
        valueInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.updateFilterCondition(conditionId, e.target.dataset.field, e.target.value);
            });
        });

        // 日期范围输入框
        const startDateInput = conditionDiv.querySelector('[data-field="startDate"]');
        const endDateInput = conditionDiv.querySelector('[data-field="endDate"]');

        if (startDateInput && endDateInput) {
            const updateDateRange = () => {
                const startDate = startDateInput.value;
                const endDate = endDateInput.value;
                const dateRange = `${startDate},${endDate}`;
                this.updateFilterCondition(conditionId, 'value', dateRange);
            };

            startDateInput.addEventListener('change', updateDateRange);
            endDateInput.addEventListener('change', updateDateRange);
        }
    }

    /**
     * 更新操作符选项
     */
    updateOperatorOptions(conditionId, fieldName) {
        const condition = this.filterConditions.find(c => c.id === conditionId);
        if (!condition) return;

        const operatorSelect = document.querySelector(`[data-condition-id="${conditionId}"][data-field="operator"]`);
        if (operatorSelect) {
            operatorSelect.innerHTML = this.getOperatorOptions('', fieldName);
            // 重置操作符和值
            condition.operator = '=';
            condition.value = '';
            this.updateValueInput(conditionId, '=');
            this.generateSQL();
        }
    }

    /**
     * 更新值输入控件
     */
    updateValueInput(conditionId, operator) {
        const condition = this.filterConditions.find(c => c.id === conditionId);
        if (!condition) return;

        condition.operator = operator;
        condition.value = ''; // 重置值

        const valueContainer = document.querySelector(`[data-condition-id="${conditionId}"].filter-value-container`);
        if (valueContainer) {
            valueContainer.innerHTML = this.createValueInput(condition);
            // 重新绑定事件
            this.bindValueInputEvents(valueContainer.closest('.filter-condition'), conditionId);
        }

        this.generateSQL();
    }

    /**
     * 更新筛选条件
     */
    updateFilterCondition(conditionId, field, value) {
        const condition = this.filterConditions.find(c => c.id === conditionId);
        if (condition) {
            condition[field] = value;
            this.generateSQL();
            console.log('筛选条件更新:', this.filterConditions);
        }
    }

    /**
     * 清除筛选条件
     */
    clearFilterConditions() {
        this.filterConditions = [];
        this.renderFilterConditions();
    }

    /**
     * 重置所有字段选择
     */
    resetFieldSelections() {
        // 清除所有字段角色
        this.fieldRoles = {};

        // 更新所有字段的UI状态
        this.fields.forEach(field => {
            this.updateFieldItemAppearance(field.name, null);
        });

        // 重新生成SQL
        this.generateSQL();

        console.log('重置所有字段选择');
    }

    /**
     * 生成SQL查询
     */
    generateSQL() {
        if (!this.selectedTable) {
            document.getElementById('customSql').value = '';
            return;
        }

        let sql;
        if (this.aggregationConfig.enabled) {
            sql = this.generateAggregationSQL();
        } else {
            const selectClause = this.generateSelectClause();
            const fromClause = `FROM ${this.selectedTable.name}`;
            const whereClause = this.generateWhereClause();

            sql = `SELECT ${selectClause}\n${fromClause}`;
            if (whereClause) {
                sql += `\n${whereClause}`;
            }
        }

        // 添加输出限制
        sql = this.addLimitClause(sql);

        document.getElementById('customSql').value = sql;
        console.log('生成SQL:', sql);
    }

    /**
     * 生成SELECT子句
     */
    generateSelectClause() {
        const selectedFields = Object.keys(this.fieldRoles).filter(fieldName =>
            this.fieldRoles[fieldName] === 'label' || this.fieldRoles[fieldName] === 'value'
        );

        if (selectedFields.length === 0) {
            return this.applyDateFormatting('*');
        }

        const formattedFields = selectedFields.map(fieldName => {
            return this.formatFieldForSelect(fieldName);
        });

        return formattedFields.join(', ');
    }

    /**
     * 为SELECT子句格式化字段
     */
    formatFieldForSelect(fieldName) {
        const dateField = document.getElementById('dateField')?.value;
        const dateFormat = document.getElementById('dateFormat')?.value;

        if (dateField && dateFormat && fieldName === dateField) {
            // 使用不同的别名避免与原始字段名冲突
            // 这样ORDER BY可以明确引用原始字段，而不是格式化后的别名
            return `DATE_FORMAT(${fieldName}, '${dateFormat}') as formatted_${fieldName}`;
        }

        return fieldName;
    }

    /**
     * 应用日期格式化到SELECT子句
     */
    applyDateFormatting(selectClause) {
        const dateField = document.getElementById('dateField')?.value;
        const dateFormat = document.getElementById('dateFormat')?.value;

        if (!dateField || !dateFormat || selectClause === '*') {
            return selectClause;
        }

        // 如果是SELECT *，需要特殊处理
        if (selectClause === '*') {
            // 获取所有字段，然后格式化日期字段
            const allFields = this.fields.map(field => {
                if (field.name === dateField) {
                    return `DATE_FORMAT(${field.name}, '${dateFormat}') as ${field.name}`;
                }
                return field.name;
            });
            return allFields.join(', ');
        }

        return selectClause;
    }

    /**
     * 生成聚合SQL查询
     */
    generateAggregationSQL() {
        const labelFields = Object.keys(this.fieldRoles).filter(fieldName =>
            this.fieldRoles[fieldName] === 'label'
        );
        const valueFields = Object.keys(this.fieldRoles).filter(fieldName =>
            this.fieldRoles[fieldName] === 'value'
        );

        if (labelFields.length === 0 || valueFields.length === 0) {
            return 'SELECT * FROM ' + this.selectedTable.name;
        }

        const labelField = labelFields[0];
        const valueField = valueFields[0];
        const aggregationType = this.aggregationConfig.type;
        const timeField = this.aggregationConfig.timeField;
        const whereClause = this.generateWhereClause();

        // 如果是差值计算类型，使用专门的差值计算SQL
        if (aggregationType === 'HOURLY_DIFF') {
            return this.generateHourlyDiffSQL(labelField, valueField, timeField, whereClause);
        }
        // 如果有时间字段且聚合类型是MAX或MIN，使用窗口函数获取最新记录
        else if (timeField && (aggregationType === 'MAX' || aggregationType === 'MIN')) {
            return this.generateWindowFunctionSQL(labelField, valueField, timeField, aggregationType, whereClause);
        } else {
            // 简单聚合查询
            return this.generateSimpleAggregationSQL(labelField, valueField, aggregationType, whereClause);
        }
    }

    /**
     * 生成窗口函数SQL（用于获取最新记录）
     */
    generateWindowFunctionSQL(labelField, valueField, timeField, aggregationType, whereClause) {
        const orderDirection = aggregationType === 'MAX' ? 'DESC' : 'ASC';

        // 格式化字段
        const formattedLabelField = this.formatFieldForSelect(labelField);
        const formattedValueField = this.formatFieldForSelect(valueField);

        // 检查是否需要添加其他日期格式化字段
        const dateField = document.getElementById('dateField')?.value;
        const dateFormat = document.getElementById('dateFormat')?.value;

        let selectFields = [formattedLabelField, formattedValueField];

        // 如果日期字段不是标签字段或数值字段，单独添加
        if (dateField && dateFormat && dateField !== labelField && dateField !== valueField) {
            const formattedDateField = `DATE_FORMAT(${dateField}, '${dateFormat}') as ${dateField}`;
            selectFields.push(formattedDateField);
        }

        let sql = `SELECT ${selectFields.join(', ')}\n`;
        sql += `FROM (\n`;
        sql += `    SELECT *, ROW_NUMBER() OVER (\n`;
        sql += `        PARTITION BY ${labelField} \n`;
        sql += `        ORDER BY ${valueField} ${orderDirection}, ${timeField} DESC\n`;
        sql += `    ) as rn\n`;
        sql += `    FROM ${this.selectedTable.name}`;

        if (whereClause) {
            sql += `\n    ${whereClause}`;
        }

        sql += `\n) t\n`;
        sql += `WHERE rn = 1`;

        return sql;
    }

    /**
     * 生成简单聚合SQL
     */
    generateSimpleAggregationSQL(labelField, valueField, aggregationType, whereClause) {
        // 格式化字段
        const formattedLabelField = this.formatFieldForSelect(labelField);
        const formattedValueField = `${aggregationType}(${valueField}) as ${valueField}`;

        // 检查是否需要添加其他日期格式化字段
        const dateField = document.getElementById('dateField')?.value;
        const dateFormat = document.getElementById('dateFormat')?.value;

        let selectFields = [formattedLabelField, formattedValueField];

        // 如果日期字段不是标签字段或数值字段，单独添加
        if (dateField && dateFormat && dateField !== labelField && dateField !== valueField) {
            const formattedDateField = `DATE_FORMAT(${dateField}, '${dateFormat}') as formatted_${dateField}`;
            selectFields.push(formattedDateField);
        }

        let sql = `SELECT ${selectFields.join(', ')}\n`;
        sql += `FROM ${this.selectedTable.name}`;

        if (whereClause) {
            sql += `\n${whereClause}`;
        }

        sql += `\nGROUP BY ${labelField}`;

        // 如果日期字段不是标签字段，也需要在GROUP BY中包含
        if (dateField && dateField !== labelField && dateField !== valueField) {
            sql += `, ${dateField}`;
        }

        return sql;
    }

    /**
     * 生成每小时产能差值SQL
     */
    generateHourlyDiffSQL(labelField, valueField, timeField, whereClause) {
        // 输入验证
        if (!timeField) {
            throw new Error('差值计算需要选择时间字段');
        }
        if (!labelField) {
            throw new Error('差值计算需要选择标签字段');
        }
        if (!valueField) {
            throw new Error('差值计算需要选择数值字段');
        }

        const timeInterval = this.aggregationConfig.timeInterval || 'HOUR';
        const ignoreNegative = this.aggregationConfig.ignoreNegative !== false;

        // 根据时间间隔生成时间格式化字符串
        let timeFormat;
        switch (timeInterval) {
            case 'HOUR':
                timeFormat = '%Y-%m-%d %H:00:00';
                break;
            case 'DAY':
                timeFormat = '%Y-%m-%d 00:00:00';
                break;
            case 'WEEK':
                timeFormat = '%Y-%u周';
                break;
            case 'MONTH':
                timeFormat = '%Y-%m-01 00:00:00';
                break;
            default:
                timeFormat = '%Y-%m-%d %H:00:00';
        }

        // 构建CTE查询 - 性能优化版本
        let sql = `WITH interval_max AS (\n`;
        sql += `    SELECT \n`;
        sql += `        ${labelField},\n`;
        sql += `        DATE_FORMAT(${timeField}, '${timeFormat}') as time_interval,\n`;
        sql += `        MAX(${valueField}) as max_value\n`;
        sql += `    FROM ${this.selectedTable.name}\n`;

        if (whereClause) {
            sql += `    ${whereClause}\n`;
        }

        sql += `    GROUP BY ${labelField}, DATE_FORMAT(${timeField}, '${timeFormat}')\n`;
        sql += `    ORDER BY ${labelField}, time_interval\n`;  // 添加排序优化窗口函数性能
        sql += `),\n`;
        sql += `interval_diff AS (\n`;
        sql += `    SELECT \n`;
        sql += `        ${labelField},\n`;
        sql += `        time_interval,\n`;
        sql += `        max_value,\n`;
        sql += `        LAG(max_value, 1, 0) OVER (\n`;
        sql += `            PARTITION BY ${labelField} \n`;
        sql += `            ORDER BY time_interval\n`;
        sql += `        ) as prev_value,\n`;
        sql += `        max_value - LAG(max_value, 1, 0) OVER (\n`;
        sql += `            PARTITION BY ${labelField} \n`;
        sql += `            ORDER BY time_interval\n`;
        sql += `        ) as interval_output\n`;
        sql += `    FROM interval_max\n`;
        sql += `)\n`;
        sql += `SELECT \n`;
        sql += `    ${labelField},\n`;
        sql += `    time_interval,\n`;

        if (ignoreNegative) {
            sql += `    CASE WHEN interval_output < 0 THEN 0 ELSE interval_output END as interval_output\n`;
        } else {
            sql += `    interval_output\n`;
        }

        sql += `FROM interval_diff\n`;
        sql += `WHERE interval_output IS NOT NULL\n`;
        sql += `ORDER BY ${labelField}, time_interval DESC`;

        return sql;
    }

    /**
     * 添加智能排序和LIMIT子句
     */
    addLimitClause(sql) {
        const outputLimitElement = document.getElementById('outputLimit');
        if (!outputLimitElement) {
            return sql;
        }

        const outputLimit = parseInt(outputLimitElement.value);
        if (!outputLimit || outputLimit <= 0) {
            return sql;
        }

        // 检查SQL是否已经包含LIMIT子句
        if (sql.toLowerCase().includes('limit')) {
            return sql;
        }

        // 添加智能排序和限制
        return this.addSmartOrderByAndLimit(sql, outputLimit);
    }

    /**
     * 添加智能排序和限制
     */
    addSmartOrderByAndLimit(sql, limit) {
        try {
            const sqlLower = sql.toLowerCase();

            // 识别可用的时间字段
            const timeField = this.identifyTimeField(sql);

            // 根据SQL类型选择处理策略
            if (this.isCTEQuery(sql)) {
                // CTE查询：在最外层添加ORDER BY + LIMIT
                return this.addOrderByLimitToCTE(sql, timeField, limit);
            } else if (this.isWindowFunctionQuery(sql)) {
                // 窗口函数查询：在最外层添加ORDER BY + LIMIT
                return this.addOrderByLimitToWindowFunction(sql, timeField, limit);
            } else if (this.isAggregationQuery(sql)) {
                // 聚合查询：在GROUP BY后添加ORDER BY + LIMIT
                return this.addOrderByLimitToAggregation(sql, timeField, limit);
            } else {
                // 简单查询：直接添加ORDER BY + LIMIT
                return this.addOrderByLimitToSimpleQuery(sql, timeField, limit);
            }
        } catch (error) {
            console.error('添加智能排序失败，使用简单LIMIT:', error);
            return sql + `\nLIMIT ${limit}`;
        }
    }

    /**
     * 识别时间字段
     */
    identifyTimeField(sql) {
        // 优先级顺序的时间字段
        const timeFieldCandidates = [
            'timestamp', 'created_at', 'updated_at', 'create_time', 'update_time',
            'date_created', 'date_updated', 'time', 'datetime', 'id'
        ];

        // 从SQL中提取字段名
        const sqlLower = sql.toLowerCase();

        // 首先检查是否有明确指定的时间字段（在聚合配置中）
        const timeFieldElement = document.getElementById('timeField');
        if (timeFieldElement && timeFieldElement.value) {
            return timeFieldElement.value;
        }

        // 从字段配置器中获取可用字段
        if (this.fields && this.fields.length > 0) {
            for (const candidate of timeFieldCandidates) {
                const field = this.fields.find(f => f.name.toLowerCase() === candidate);
                if (field) {
                    console.log('识别到时间字段:', field.name);
                    return field.name;
                }
            }
        }

        // 从SQL中查找时间字段
        for (const candidate of timeFieldCandidates) {
            if (sqlLower.includes(candidate)) {
                console.log('从SQL中识别到时间字段:', candidate);
                return candidate;
            }
        }

        // 默认使用id字段
        console.log('未找到明确的时间字段，使用id作为排序字段');
        return 'id';
    }



    /**
     * 判断是否为CTE查询
     */
    isCTEQuery(sql) {
        const sqlLower = sql.toLowerCase();
        return sqlLower.includes('with ') && sqlLower.includes(' as (');
    }

    /**
     * 判断是否为窗口函数查询
     */
    isWindowFunctionQuery(sql) {
        const sqlLower = sql.toLowerCase();
        return sqlLower.includes('row_number()') && sqlLower.includes('over');
    }

    /**
     * 判断是否为聚合查询
     */
    isAggregationQuery(sql) {
        const sqlLower = sql.toLowerCase();
        return sqlLower.includes('group by') ||
               /\b(max|min|avg|sum|count)\s*\(/i.test(sql);
    }

    /**
     * 为简单查询添加ORDER BY + LIMIT
     */
    addOrderByLimitToSimpleQuery(sql, timeField, limit) {
        // 检查是否已有ORDER BY
        const sqlLower = sql.toLowerCase();
        if (sqlLower.includes('order by')) {
            // 已有ORDER BY，直接添加LIMIT
            return sql + `\nLIMIT ${limit}`;
        }

        // 强制使用原始时间字段进行排序
        const orderByField = this.getOrderByFieldForTimeSort(timeField);

        console.log(`添加ORDER BY排序，使用字段: ${orderByField}`);
        return sql + `\nORDER BY ${orderByField} DESC\nLIMIT ${limit}`;
    }

    /**
     * 获取用于ORDER BY的字段名（强制使用原始字段）
     */
    getOrderByFieldForTimeSort(timeField) {
        // 检查是否配置了日期格式化
        const dateField = document.getElementById('dateField')?.value;
        const dateFormat = document.getElementById('dateFormat')?.value;

        // 如果配置了日期格式化，且当前时间字段就是被格式化的字段
        if (dateField && dateFormat && timeField === dateField) {
            console.log(`检测到日期格式化配置，强制使用原始字段 ${timeField} 进行排序`);
            // 强制返回原始字段名，确保ORDER BY使用原始时间字段而不是格式化后的别名
            // 这样可以避免按格式化字符串排序导致的时间顺序错误
            return timeField;
        }

        // 没有日期格式化或不是被格式化的字段，直接使用原字段
        return timeField;
    }

    /**
     * 为聚合查询添加ORDER BY + LIMIT
     */
    addOrderByLimitToAggregation(sql, timeField, limit) {
        const sqlLower = sql.toLowerCase();

        // 检查是否已有ORDER BY
        if (sqlLower.includes('order by')) {
            return sql + `\nLIMIT ${limit}`;
        }

        // 强制使用原始时间字段进行排序
        const orderByField = this.getOrderByFieldForTimeSort(timeField);

        // 查找GROUP BY的位置
        const groupByMatch = sql.match(/GROUP\s+BY\s+([^;]+?)(?:\s|$)/i);
        if (groupByMatch) {
            // 在聚合查询中，使用聚合函数对原始时间字段排序
            // 强制使用原始字段，避免对格式化后的字符串进行聚合排序
            const orderByClause = `\nORDER BY MAX(${orderByField}) DESC`;
            console.log(`聚合查询ORDER BY使用: MAX(${orderByField})`);
            return sql + orderByClause + `\nLIMIT ${limit}`;
        }

        // 如果没有GROUP BY但有聚合函数，直接添加ORDER BY
        console.log(`聚合查询ORDER BY使用: ${orderByField}`);
        return sql + `\nORDER BY ${orderByField} DESC\nLIMIT ${limit}`;
    }

    /**
     * 为CTE查询添加ORDER BY + LIMIT
     */
    addOrderByLimitToCTE(sql, timeField, limit) {
        // CTE查询通常已经有复杂的排序逻辑
        // 在最外层添加ORDER BY + LIMIT来获取最新记录
        const sqlLower = sql.toLowerCase();

        // 检查是否已有最外层的ORDER BY
        if (sqlLower.includes('order by') && !sqlLower.includes('over (')) {
            return sql + `\nLIMIT ${limit}`;
        }

        // 对于差值计算查询，通常已经有合适的排序
        // 直接添加LIMIT即可
        console.log(`CTE查询添加LIMIT: ${limit}`);
        return sql + `\nLIMIT ${limit}`;
    }

    /**
     * 为窗口函数查询添加ORDER BY + LIMIT
     */
    addOrderByLimitToWindowFunction(sql, timeField, limit) {
        // 窗口函数查询通常已经有复杂的排序逻辑
        // 在最外层添加ORDER BY + LIMIT来获取最新记录
        const sqlLower = sql.toLowerCase();

        // 检查是否已有最外层的ORDER BY
        if (sqlLower.includes('order by') && !sqlLower.includes('over')) {
            return sql + `\nLIMIT ${limit}`;
        }

        // 强制使用原始时间字段进行排序
        const orderByField = this.getOrderByFieldForTimeSort(timeField);

        // 在最外层添加ORDER BY + LIMIT，强制使用原始时间字段
        console.log(`窗口函数查询ORDER BY使用: ${orderByField}`);
        return sql + `\nORDER BY ${orderByField} DESC\nLIMIT ${limit}`;
    }

    /**
     * 生成WHERE子句
     */
    generateWhereClause() {
        const validConditions = this.filterConditions.filter(c =>
            c.field && c.operator && (c.value || c.operator.includes('NULL') || this.isTimePresetOperator(c.operator))
        );

        if (validConditions.length === 0) {
            return '';
        }

        let whereClause = 'WHERE ';
        validConditions.forEach((condition, index) => {
            if (index > 0 && condition.logicOperator) {
                whereClause += ` ${condition.logicOperator} `;
            }

            whereClause += this.generateConditionSQL(condition);
        });

        return whereClause;
    }

    /**
     * 检查是否为时间预设操作符
     */
    isTimePresetOperator(operator) {
        const timePresetOperators = ['TODAY', 'THIS_WEEK', 'THIS_MONTH', 'THIS_QUARTER', 'THIS_YEAR'];
        return timePresetOperators.includes(operator);
    }

    /**
     * 生成单个条件的SQL
     */
    generateConditionSQL(condition) {
        const { field, operator, value } = condition;

        // 处理NULL操作符
        if (operator.includes('NULL')) {
            return `${field} ${operator}`;
        }

        // 处理时间预设操作符
        switch (operator) {
            case 'TODAY':
                return `DATE(${field}) = CURDATE()`;
            case 'THIS_WEEK':
                return `YEARWEEK(${field}, 1) = YEARWEEK(CURDATE(), 1)`;
            case 'THIS_MONTH':
                return `YEAR(${field}) = YEAR(CURDATE()) AND MONTH(${field}) = MONTH(CURDATE())`;
            case 'THIS_QUARTER':
                return `QUARTER(${field}) = QUARTER(CURDATE()) AND YEAR(${field}) = YEAR(CURDATE())`;
            case 'THIS_YEAR':
                return `YEAR(${field}) = YEAR(CURDATE())`;
            case 'CUSTOM_DATE':
                return `DATE(${field}) = '${value}'`;
            case 'DATE_RANGE':
                const dates = value.split(',');
                if (dates.length === 2 && dates[0] && dates[1]) {
                    return `${field} BETWEEN '${dates[0]}' AND '${dates[1]}'`;
                }
                return `1=1`; // 无效的日期范围，返回总是真的条件
        }

        // 处理LIKE操作符
        if (operator === 'LIKE' || operator === 'NOT LIKE') {
            return `${field} ${operator} '%${value}%'`;
        }

        // 处理IN操作符
        if (operator === 'IN' || operator === 'NOT IN') {
            return `${field} ${operator} (${value})`;
        }

        // 处理普通操作符
        return `${field} ${operator} '${value}'`;
    }

    /**
     * 验证SQL
     */
    async validateSQL() {
        const sql = document.getElementById('customSql').value.trim();
        if (!sql) {
            this.showSqlStatus('请先生成或输入SQL语句', 'invalid');
            return;
        }

        // 这里可以调用后端API验证SQL
        // 暂时只做基本的语法检查
        if (sql.toLowerCase().includes('select') && sql.toLowerCase().includes('from')) {
            this.showSqlStatus('SQL语法基本正确', 'valid');
        } else {
            this.showSqlStatus('SQL语法可能有误', 'invalid');
        }
    }

    /**
     * 显示SQL状态
     */
    showSqlStatus(message, type) {
        let statusElement = document.querySelector('.sql-status');
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.className = 'sql-status';
            document.getElementById('customSql').parentNode.appendChild(statusElement);
        }

        statusElement.textContent = message;
        statusElement.className = `sql-status ${type}`;

        // 3秒后清除状态
        setTimeout(() => {
            statusElement.textContent = '';
            statusElement.className = 'sql-status';
        }, 3000);
    }

    /**
     * 设置筛选条件（用于编辑模式恢复）
     */
    setFilterConditions(conditions) {
        console.log('设置筛选条件:', conditions);

        if (conditions && Array.isArray(conditions)) {
            this.filterConditions = conditions.map(condition => ({
                id: condition.id || Date.now() + Math.random(),
                field: condition.field || '',
                operator: condition.operator || '=',
                value: condition.value || '',
                logicOperator: condition.logicOperator || null
            }));
        } else {
            this.filterConditions = [];
        }

        // 重新渲染筛选条件UI
        this.renderFilterConditions();

        // 重新生成SQL
        this.generateSQL();

        console.log('筛选条件设置完成:', this.filterConditions);
    }

    /**
     * 获取配置结果
     */
    getConfiguration() {
        return {
            table: this.selectedTable,
            fieldRoles: this.fieldRoles,
            filterConditions: this.filterConditions,
            aggregationConfig: this.aggregationConfig,
            sql: document.getElementById('customSql').value.trim()
        };
    }
}

// 全局实例
window.fieldConfigurator = new DataSetFieldConfigurator();
