# 每小时产能差值计算功能文档

## 功能概述

每小时产能差值计算功能是一个新增的聚合类型，专门用于处理累计产能数据，计算每个时间间隔内的实际产能输出。该功能通过分析累计值的变化，自动计算出每个时间段的增量值。

## 适用场景

- **生产设备监控**：计算设备每小时的实际产量
- **累计数据分析**：将累计值转换为增量值进行分析
- **产能统计报表**：生成基于时间间隔的产能报告
- **设备效率分析**：监控设备在不同时间段的生产效率

## 功能特性

### 1. 多种时间间隔支持
- **小时**：按小时计算产能差值
- **天**：按天计算产能差值
- **周**：按周计算产能差值
- **月**：按月计算产能差值

### 2. 智能负值处理
- **忽略负值**：当累计值重置或出现异常时，自动将负值设为0
- **保留负值**：保留所有计算结果，包括负值（用于异常分析）

### 3. 高性能SQL生成
- 使用CTE（公用表表达式）优化查询性能
- 利用LAG窗口函数进行高效的前值比较
- 自动处理分组和排序逻辑

## 使用方法

### 1. 基本配置

1. **启用聚合查询**：勾选"启用聚合查询"选项
2. **选择聚合类型**：在下拉菜单中选择"每小时产能差值"
3. **配置字段角色**：
   - 标签字段：选择设备名称或分组字段
   - 数值字段：选择累计产能值字段
   - 时间字段：选择时间戳字段

### 2. 高级配置

#### 时间间隔设置
```
小时：计算每小时的产能差值
天：计算每天的产能差值
周：计算每周的产能差值
月：计算每月的产能差值
```

#### 负值处理
- **启用忽略负值**：推荐设置，自动处理累计值重置情况
- **禁用忽略负值**：保留所有计算结果，用于异常分析

### 3. 数据要求

#### 表结构要求
```sql
CREATE TABLE data_history (
    id INT PRIMARY KEY,
    device_name VARCHAR(100),    -- 设备名称（标签字段）
    value INT,                   -- 累计产能值（数值字段）
    timestamp DATETIME,          -- 时间戳（时间字段）
    -- 其他字段...
);
```

#### 数据特点
- **累计性数据**：数值字段应为累计值，随时间递增
- **连续时间序列**：时间字段应有连续的时间记录
- **设备分组**：标签字段用于区分不同设备或分组

## 生成的SQL结构

### 基本SQL模板
```sql
WITH interval_max AS (
    SELECT 
        device_name,
        DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00') as time_interval,
        MAX(value) as max_value
    FROM data_history
    GROUP BY device_name, DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00')
),
interval_diff AS (
    SELECT 
        device_name,
        time_interval,
        max_value,
        LAG(max_value, 1, 0) OVER (
            PARTITION BY device_name 
            ORDER BY time_interval
        ) as prev_value,
        max_value - LAG(max_value, 1, 0) OVER (
            PARTITION BY device_name 
            ORDER BY time_interval
        ) as interval_output
    FROM interval_max
)
SELECT 
    device_name,
    time_interval,
    CASE WHEN interval_output < 0 THEN 0 ELSE interval_output END as interval_output
FROM interval_diff
WHERE interval_output IS NOT NULL
ORDER BY device_name, time_interval DESC
```

### SQL执行步骤解析

1. **第一步（interval_max CTE）**：
   - 按设备和时间间隔分组
   - 计算每个时间间隔内的最大累计值
   - 处理同一时间间隔内的多条记录

   **时间聚合逻辑详解**：
   ```
   原始数据示例：
   设备A, 2025-07-29 09:15:23, 累计值=100
   设备A, 2025-07-29 09:32:45, 累计值=150
   设备A, 2025-07-29 09:58:12, 累计值=200
   设备A, 2025-07-29 10:05:30, 累计值=220

   聚合结果：
   设备A, 2025-07-29 09:00:00, max_value=200 (09:00-09:59内的最大值)
   设备A, 2025-07-29 10:00:00, max_value=220 (10:00-10:59内的最大值)
   ```

   **关键点**：DATE_FORMAT将时间标准化到间隔开始时间，但GROUP BY确保整个时间段内的所有数据都被聚合。

2. **第二步（interval_diff CTE）**：
   - 使用LAG窗口函数获取前一时间间隔的值
   - 计算当前间隔与前一间隔的差值
   - 按设备分区，按时间排序

3. **第三步（最终查询）**：
   - 应用负值处理逻辑
   - 过滤空值记录
   - 按设备和时间排序输出

## 性能优化建议

### 1. 数据库索引
```sql
-- 推荐创建的索引
CREATE INDEX idx_device_time ON data_history(device_name, timestamp);
CREATE INDEX idx_timestamp ON data_history(timestamp);
```

### 2. 查询优化
- 使用WHERE条件限制时间范围
- 合理设置LIMIT限制输出记录数
- 定期清理历史数据

### 3. 监控建议
- 监控查询执行时间
- 检查CTE查询的执行计划
- 根据数据量调整时间间隔

## 常见问题解决

### 1. 负值问题
**问题**：计算结果出现负值
**原因**：累计值重置或数据异常
**解决**：启用"忽略负值"选项

### 2. 性能问题
**问题**：查询执行缓慢
**原因**：数据量大或缺少索引
**解决**：添加合适的索引，使用时间过滤条件

### 3. 数据缺失
**问题**：某些时间间隔没有数据
**原因**：原始数据不连续
**解决**：检查数据采集系统，确保数据连续性

## 版本更新记录

### v1.0.0 (当前版本)
- 新增每小时产能差值计算功能
- 支持多种时间间隔（小时/天/周/月）
- 智能负值处理
- CTE优化查询性能
- 完整的输入验证和错误处理

## 技术支持

如遇到问题或需要技术支持，请联系开发团队或查看相关技术文档。
